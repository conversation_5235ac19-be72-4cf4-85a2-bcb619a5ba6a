import pandas as pd
import numpy as np
from model_script import *
from utilts import *

filled_df=pd.read_csv("筛选后协变量特征值列表.csv",index_col=[0])
print(filled_df.shape)
# 按location分组
grouped = filled_df.groupby('location')
# 创建字典，key是location，value是对应的分组数据框
result_dict = {location: group for location, group in grouped}
# 打印结果
for location, group in result_dict.items():
    print(f"Location: {location}")
    print(group)
    break

# 基本结构查看
print("数据类型:", type(result_dict))
print("国家数量:", len(result_dict))
print("\n所有键(国家):", list(result_dict.keys()))
# 查看详细内容
print("\n完整数据内容:")
j=0
for country, data in result_dict.items():
    print(f"\n国家: {country}")
    print(f"数据: {data.iloc[:10,:10]}")
    j+=1
    if j>=5:
        break

def scale_result_dict(result_dict, outlier_threshold=1.5, handle_outliers='cap'):
    """
    将 result_dict 中所有国家的特征数据标准化到 [0, 1] 范围，并在标准化前检测异常值
    :param result_dict: 输入的原始数据字典
    :param outlier_threshold: IQR 倍数，用于定义异常值阈值（默认 1.5）
    :param handle_outliers: 处理异常值的方式，'cap'（限制到边界）、'remove'（移除）、'keep'（保留）
    :return: 标准化后的新字典 result_dict_scale
    """
    result_dict_scale = {}
    scaler = MinMaxScaler()

    for country, df in result_dict.items():
        # 提取特征列（从第2列开始）
        features = df.iloc[:, 2:].copy()
        
        # 检测和处理异常值
        if handle_outliers in ['cap', 'remove']:
            for col in features.columns:
                # 计算四分位距 (IQR)
                Q1 = features[col].quantile(0.25)
                Q3 = features[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - outlier_threshold * IQR
                upper_bound = Q3 + outlier_threshold * IQR

                # 检测异常值
                outliers = (features[col] < lower_bound) | (features[col] > upper_bound)
                if outliers.any():
                    print(f"Country {country}, Column {col}: Detected {outliers.sum()} outliers")
                
                # 处理异常值
                if handle_outliers == 'cap':
                    # 限制到边界值
                    features.loc[features[col] < lower_bound, col] = lower_bound
                    features.loc[features[col] > upper_bound, col] = upper_bound
                elif handle_outliers == 'remove':
                    # 移除包含异常值的行（注意：这会影响整个 DataFrame）
                    features = features[~outliers]
                    # 如果移除了行，同步更新 location 和 year
                    df = df.loc[features.index]

        # 标准化到 [0, 1]
        scaled_features = scaler.fit_transform(features)
        
        # 创建新的 DataFrame，保留 location 和 year 列
        scaled_df = pd.DataFrame(scaled_features, columns=features.columns)
        scaled_df.insert(0, 'location', df['location'].values)
        scaled_df.insert(1, 'year', df['year'].values)
        result_dict_scale[country] = scaled_df
    
    return result_dict_scale

result_dict_scale = scale_result_dict(result_dict, outlier_threshold=10, handle_outliers='cap')

import torch
import torch.nn.functional as F
from torch_geometric.nn import GCNConv
from torch_geometric.data import Data
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
import pandas as pd

def preprocess_data(result_dict, years=range(1960, 2015), exclude_cols=['location', 'year']):
    countries = list(result_dict.keys())
    sample_df = result_dict[countries[0]]
    feature_cols = [col for col in sample_df.columns if col not in exclude_cols]
    num_features = len(feature_cols) * len(years)
    features = np.zeros((len(countries), num_features))
    for i, country in enumerate(countries):
        df = result_dict[country]
        for j, year in enumerate(years):
            for k, col in enumerate(feature_cols):
                value = df[df['year'] == float(year)][col].values
                features[i, j * len(feature_cols) + k] = value[0] if len(value) > 0 else 0
    features = (features - features.mean(axis=0)) / (features.std(axis=0) + 1e-8)
    return torch.tensor(features, dtype=torch.float), countries, feature_cols

def build_graph(features, k=5):
    sim_matrix = cosine_similarity(features.numpy())
    edge_index = []
    for i in range(len(sim_matrix)):
        neighbors = np.argsort(sim_matrix[i])[::-1][1:k+1]
        for j in neighbors:
            edge_index.append([i, j])
            edge_index.append([j, i])
    edge_index = torch.tensor(edge_index, dtype=torch.long).t().contiguous()
    return edge_index

class GCNModel(torch.nn.Module):
    def __init__(self, in_channels, hidden_channels, out_channels):
        super(GCNModel, self).__init__()
        self.conv1 = GCNConv(in_channels, hidden_channels)
        self.conv2 = GCNConv(hidden_channels, out_channels)
    
    def forward(self, data):
        x, edge_index = data.x, data.edge_index
        x = self.conv1(x, edge_index)
        x = F.relu(x)
        x = F.dropout(x, p=0.5, training=self.training)
        x = self.conv2(x, edge_index)
        return x

def train_gcn(data, model, epochs=300, lr=0.005, temperature=0.07, neg_samples=10):
    optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=5e-4)
    model.train()
    for epoch in range(epochs):
        optimizer.zero_grad()
        out = model(data)
        loss = 0
        num_nodes = out.size(0)
        for i, j in data.edge_index.t():
            pos_sim = F.cosine_similarity(out[i], out[j], dim=0) / temperature
            neg_idx = torch.randint(0, num_nodes, (neg_samples,), device=out.device)
            neg_sim = F.cosine_similarity(out[i].unsqueeze(0), out[neg_idx]).mean() / temperature
            loss += -torch.log(torch.exp(pos_sim) / (torch.exp(pos_sim) + torch.exp(neg_sim)))
        loss = loss / data.edge_index.size(1)
        reg_loss = torch.norm(out, p=2).mean() * 0.001
        total_loss = loss + reg_loss
        total_loss.backward()
        optimizer.step()
        if epoch % 20 == 0:
            print(f"Epoch {epoch}, Loss: {total_loss.item():.4f}")
    return out

def get_embeddings(model, data):
    model.eval()
    with torch.no_grad():
        embeddings = model(data)
    return embeddings

def main(result_dict):
    years = range(1960, 2015)
    hidden_channels = 64
    out_channels = 32
    k_neighbors = 5
    print("Step 1: Preprocessing data with all features...")
    features, countries, feature_cols = preprocess_data(result_dict, years)
    in_channels = features.shape[1]
    print(f"Features shape: {features.shape}")
    print(f"Feature columns included: {feature_cols}")
    print("Step 2: Building graph with k-nearest neighbors...")
    edge_index = build_graph(features, k=k_neighbors)
    data = Data(x=features, edge_index=edge_index)
    print(f"Number of edges: {edge_index.size(1)}")
    print("Step 3: Initializing GCN model...")
    model = GCNModel(in_channels=in_channels, hidden_channels=hidden_channels, out_channels=out_channels)
    print("Step 4: Training GCN model with optimized InfoNCE loss...")
    embeddings = train_gcn(data, model, epochs=300)
    print("Step 5: Getting embeddings...")
    embeddings = get_embeddings(model, data)
    print(f"Embeddings shape: {embeddings.shape}")
    embeddings_np = embeddings.numpy()
    print("Embedding std (per dimension):", embeddings_np.std(axis=0).mean())
    norms = np.linalg.norm(embeddings_np, axis=1)
    print("Embedding norms (first 5):", norms[:5])
    embedding_dict = {country: embeddings[i].numpy() for i, country in enumerate(countries)}
    print("\nSample embeddings:")
    for country in countries[:3]:
        print(f"{country}: {embedding_dict[country][:5]}...")
    return embedding_dict

if __name__ == "__main__":
    embedding_dict = main(result_dict_scale)

cor_space_country=pd.DataFrame.from_dict(embedding_dict)
cor_space_country=cor_space_country.T
cor_space_country.to_csv("所有国家空间协变量emb.csv")

cor_space_country.head()

import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.manifold import TSNE
import matplotlib.pyplot as plt

def compute_similarity_matrix(embedding_dict):
    countries = list(embedding_dict.keys())
    embeddings = np.array([embedding_dict[country] for country in countries])
    sim_matrix = cosine_similarity(embeddings)
    return sim_matrix, countries

def find_similar_pairs(sim_matrix, countries, top_n=10, min_regions=4):
    num_countries = len(countries)
    sim_pairs = []
    region_dict = {
        'Africa': ['Burkina Faso', 'Algeria', 'Kenya', 'Nigeria'],
        'Europe': ['Poland', 'Albania', 'Germany', 'France'],
        'Americas': ['Grenada', 'Colombia', 'Canada', 'United States of America'],
        'Asia': ['Afghanistan', 'China', 'India', 'Japan'],
        'Oceania': ['Australia', 'New Zealand']
    }
    country_to_region = {}
    for region, region_countries in region_dict.items():
        for country in region_countries:
            if country in countries:
                country_to_region[country] = region
    for i in range(num_countries):
        for j in range(i + 1, num_countries):
            sim_pairs.append((countries[i], countries[j], sim_matrix[i, j]))
    sim_pairs = sorted(sim_pairs, key=lambda x: x[2], reverse=True)
    selected_pairs = []
    covered_regions = set()
    for pair in sim_pairs:
        c1, c2, sim = pair
        r1 = country_to_region.get(c1, 'Other')
        r2 = country_to_region.get(c2, 'Other')
        if (len(covered_regions) < min_regions or sim > 0.99) and len(selected_pairs) < top_n:
            selected_pairs.append(pair)
            covered_regions.add(r1)
            covered_regions.add(r2)
        if len(selected_pairs) >= top_n and len(covered_regions) >= min_regions:
            break
    return selected_pairs

def visualize_embeddings(embedding_dict, sim_pairs):
    countries = list(embedding_dict.keys())
    embeddings = np.array([embedding_dict[country] for country in countries])
    tsne = TSNE(n_components=2, random_state=42, perplexity=30)
    embeddings_2d = tsne.fit_transform(embeddings)
    plt.figure(figsize=(24, 20))
    plt.scatter(embeddings_2d[:, 0], embeddings_2d[:, 1], c='lightgray', s=30, alpha=0.5, label='Countries')
    colors = plt.cm.tab10(np.linspace(0, 1, len(sim_pairs)))
    for idx, (c1, c2, sim) in enumerate(sim_pairs):
        i1 = countries.index(c1)
        i2 = countries.index(c2)
        plt.scatter(embeddings_2d[i1, 0], embeddings_2d[i1, 1], c=[colors[idx]], s=100, label=f'{c1} (sim={sim:.3f})')
        plt.scatter(embeddings_2d[i2, 0], embeddings_2d[i2, 1], c=[colors[idx]], s=100)
        plt.plot([embeddings_2d[i1, 0], embeddings_2d[i2, 0]], 
                 [embeddings_2d[i1, 1], embeddings_2d[i2, 1]], 
                 c=colors[idx], linestyle='--', alpha=0.7)
    for i, country in enumerate(countries):
        plt.annotate(country, 
                     (embeddings_2d[i, 0], embeddings_2d[i, 1]), 
                     fontsize=8, 
                     ha='right', 
                     va='bottom', 
                     alpha=0.7, 
                     color='black',
                     xytext=(5, 5), 
                     textcoords='offset points')
    plt.title("t-SNE Visualization of Country Embeddings (All Countries Labeled)", fontsize=16)
    plt.xlabel("t-SNE Component 1", fontsize=12)
    plt.ylabel("t-SNE Component 2", fontsize=12)
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
    plt.tight_layout()
    plt.grid(True, linestyle='--', alpha=0.3)
    plt.show()

def visualize_embedding_results(embedding_dict, top_n=10):
    print("Step 1: Computing similarity matrix...")
    sim_matrix, countries = compute_similarity_matrix(embedding_dict)
    print(f"Step 2: Finding top {top_n} similar country pairs with diverse regions...")
    top_sim_pairs = find_similar_pairs(sim_matrix, countries, top_n=top_n, min_regions=4)
    print(f"\nTop {top_n} similar country pairs:")
    for c1, c2, sim in top_sim_pairs:
        print(f"{c1} - {c2}: Cosine Similarity = {sim:.4f}")
    print("Step 3: Visualizing embeddings with t-SNE...")
    visualize_embeddings(embedding_dict, top_sim_pairs)

if __name__ == "__main__":
    visualize_embedding_results(embedding_dict, top_n=10)

#上图放大看可以看到，相似的国家确实在一起，比如左下角欧美发达国家都在一起。

import pandas as pd
import numpy as np
import torch
import gpytorch
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import MinMaxScaler
import gc

cor_space_country = pd.read_csv("所有国家空间协变量emb.csv", index_col=0)
def split_train_test_years(df, test_size=-5):
    """划分训练和测试年份"""
    years = df['year'].sort_values().unique()
    if abs(test_size) >= len(years):
        raise ValueError("Test size must be smaller than the total number of years")
    test_years = years[test_size:].tolist()
    train_years = years[:test_size].tolist()
    return train_years, test_years

def get_train_test_data(df, train_years, test_years):
    """根据年份划分训练和测试数据集"""
    train_df = df[df['year'].isin(train_years)]
    test_df = df[df['year'].isin(test_years)]
    return train_df, test_df

def standardize_data(df, feature_start_idx=2):
    """标准化特征数据"""
    scaler = MinMaxScaler()
    features_scaled = scaler.fit_transform(df.iloc[:, feature_start_idx:])
    return pd.DataFrame(features_scaled, columns=df.iloc[:, feature_start_idx:].columns), scaler

def create_gp_model(X_train, y_train, device):
    """创建并返回高斯过程模型和似然函数，适应时间+空间嵌入输入"""
    class MultitaskGPModel(gpytorch.models.ExactGP):
        def __init__(self, train_x, train_y, likelihood):
            super(MultitaskGPModel, self).__init__(train_x, train_y, likelihood)
            self.mean_module = gpytorch.means.MultitaskMean(
                gpytorch.means.ConstantMean(), num_tasks=y_train.shape[1]
            )
            self.covar_module = gpytorch.kernels.MultitaskKernel(
                gpytorch.kernels.ScaleKernel(
                    gpytorch.kernels.ProductKernel(
                        gpytorch.kernels.RBFKernel(active_dims=[0]),  # 时间维度
                        gpytorch.kernels.RBFKernel(active_dims=range(1, 33))  # 嵌入维度 (32 维)
                    )
                ),
                num_tasks=y_train.shape[1], rank=1
            )

        def forward(self, x):
            mean_x = self.mean_module(x)
            covar_x = self.covar_module(x)
            return gpytorch.distributions.MultitaskMultivariateNormal(mean_x, covar_x)

    likelihood = gpytorch.likelihoods.MultitaskGaussianLikelihood(num_tasks=y_train.shape[1]).to(device)
    model = MultitaskGPModel(X_train, y_train, likelihood).to(device)
    return model, likelihood

def train_model(model, likelihood, X_train, y_train, training_iter=80, lr=0.1):
    """训练高斯过程模型"""
    model.train()
    likelihood.train()
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)
    mll = gpytorch.mlls.ExactMarginalLogLikelihood(likelihood, model)

    for i in range(training_iter):
        optimizer.zero_grad()
        output = model(X_train)
        loss = -mll(output, y_train)
        loss.backward()
        print(f'Iter {i + 1}/{training_iter} - Loss: {loss.item():.3f}')
        optimizer.step()
    return model, likelihood

def predict_values(model, likelihood, train_len, pred_steps, embedding_vector, device):
    """使用训练好的模型进行预测，包含嵌入向量"""
    next_time_steps = torch.tensor([[train_len + 0] for i in range(pred_steps)],
                                  dtype=torch.float32).to(device)
    embedding_tensor = torch.tensor(embedding_vector, dtype=torch.float32).to(device)
    X_pred = torch.cat([next_time_steps, embedding_tensor.repeat(pred_steps, 1)], dim=1)  # [pred_steps, 33]
    
    model.eval()
    likelihood.eval()
    with torch.no_grad(), gpytorch.settings.fast_pred_var():
        next_predictions = likelihood(model(X_pred)).mean.cpu().numpy()
    return next_predictions

def cleanup_memory():
    """清理内存"""
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

def build_prediction_df(predictions, location, test_years, feature_cols):
    """构建预测结果 DataFrame"""
    pred_df = pd.DataFrame(predictions, columns=feature_cols)
    pred_df.insert(0, 'location', location)
    pred_df.insert(1, 'year', test_years)
    return pred_df

def evaluate_predictions(true_df, pred_df, feature_start_idx=2):
    """计算评估指标"""
    true_df = true_df.set_index('year')
    pred_df = pred_df.set_index('year')
    common_years = true_df.index.intersection(pred_df.index)
    feature_cols = true_df.columns[feature_start_idx:]

    mse_list, rmse_list, mae_list, r2_list = [], [], [], []
    for year in common_years:
        true_vals = true_df.loc[year, feature_cols].values
        pred_vals = pred_df.loc[year, feature_cols].values
        mse = mean_squared_error(true_vals, pred_vals)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(true_vals, pred_vals)
        r2 = r2_score(true_vals, pred_vals)
        mse_list.append(mse)
        rmse_list.append(rmse)
        mae_list.append(mae)
        r2_list.append(r2)
    
    return [mse_list, rmse_list, mae_list, r2_list]

def process_single_country(df, test_size, device, embedding_df):
    """处理单个国家的预测和评估，加入嵌入向量"""
    country = df['location'].iloc[0]
    train_years, test_years = split_train_test_years(df, test_size)
    train_df, test_df = get_train_test_data(df, train_years, test_years)

    # 标准化训练数据
    features_scaled, scaler = standardize_data(train_df)
    
    # 获取国家嵌入向量（32 维）
    embedding_vector = embedding_df.loc[country].values.reshape(1, -1)  # [1, 32]
    
    # 准备训练输入：时间 + 嵌入向量
    time_steps = np.arange(len(features_scaled)).reshape(-1, 1)  # [num_years, 1]
    embedding_repeated = np.repeat(embedding_vector, len(features_scaled), axis=0)  # [num_years, 32]
    X_train = np.hstack([time_steps, embedding_repeated])  # [num_years, 33]
    X_train = torch.tensor(X_train, dtype=torch.float32).to(device)
    y_train = torch.tensor(features_scaled.values, dtype=torch.float32).to(device)

    # 创建和训练模型
    model, likelihood = create_gp_model(X_train, y_train, device)
    model, likelihood = train_model(model, likelihood, X_train, y_train)

    # 预测
    pred_steps = len(test_years)
    predictions = predict_values(model, likelihood, len(train_df), pred_steps, embedding_vector, device)

    # 清理内存
    cleanup_memory()

    # 构建预测结果
    pred_df = build_prediction_df(predictions, country, test_years, features_scaled.columns)
    
    # 评估
    metrics = evaluate_predictions(test_df, pred_df)
    return metrics

def scale_result_dict(result_dict, outlier_threshold=1.5, handle_outliers='cap'):
    """标准化 result_dict 数据"""
    result_dict_scale = {}
    scaler = MinMaxScaler()

    for country, df in result_dict.items():
        features = df.iloc[:, 2:].copy()
        
        if handle_outliers in ['cap', 'remove']:
            for col in features.columns:
                Q1 = features[col].quantile(0.25)
                Q3 = features[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - outlier_threshold * IQR
                upper_bound = Q3 + outlier_threshold * IQR

                outliers = (features[col] < lower_bound) | (features[col] > upper_bound)
                if outliers.any():
                    print(f"Country {country}, Column {col}: Detected {outliers.sum()} outliers")
                
                if handle_outliers == 'cap':
                    features.loc[features[col] < lower_bound, col] = lower_bound
                    features.loc[features[col] > upper_bound, col] = upper_bound
                elif handle_outliers == 'remove':
                    features = features[~outliers]
                    df = df.loc[features.index]

        scaled_features = scaler.fit_transform(features)
        scaled_df = pd.DataFrame(scaled_features, columns=features.columns)
        scaled_df.insert(0, 'location', df['location'].values)
        scaled_df.insert(1, 'year', df['year'].values)
        result_dict_scale[country] = scaled_df
    
    return result_dict_scale

# 主流程
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
metric_dict = {}
test_size = -5

# 加载嵌入数据
cor_space_country = pd.read_csv("所有国家空间协变量emb.csv", index_col=[0])
for j, country in enumerate(result_dict_scale.keys(), 1):
    df_target = result_dict_scale[country]
    metrics = process_single_country(df_target, test_size, device, cor_space_country)
    metric_dict[country] = metrics

    avg_mse = np.mean(metrics[0][:5])
    avg_rmse = np.mean(metrics[1][:5])
    avg_mae = np.mean(metrics[2][:5])
    avg_r2 = np.mean(metrics[3][:5])
    print(f"Country: {country}, Avg MSE: {avg_mse:.3f}, Avg RMSE: {avg_rmse:.3f}, "
          f"Avg MAE: {avg_mae:.3f}, Avg R²: {avg_r2:.3f}")
    print(f"Processed {j}/{len(result_dict_scale)} countries")

df=pd.DataFrame.from_dict(metric_dict).T.copy()
df.to_csv("空间自变量-五年数据保存_原始.csv")

import pandas as pd
import numpy as np

mean_col_0 = df[0].apply(np.mean)
mean_col_1 = df[1].apply(np.mean)
mean_col_2 = df[2].apply(np.mean)
result_df = pd.DataFrame({
    'Country': df.index,
    'Mean_Col_0': mean_col_0,
    'Mean_Col_1': mean_col_1,
    'Mean_Col_2': mean_col_2,
    '2019-2023 R2': df[3]
})
result_df = result_df.set_index('Country')

print("任务 1：第 0、1、2 列的均值，第 3 列保留并重命名")
print(result_df)
years = [str(year) for year in range(2019, 2024)]
r2_expanded = pd.DataFrame(result_df['2019-2023 R2'].tolist(), columns=years, index=result_df.index)
result_df_expanded = pd.concat([result_df.drop('2019-2023 R2',axis=1), r2_expanded], axis=1)
result_df_expanded = result_df_expanded.copy()

print("处理后的结果：每一列包含三个均值和 2015-2023 年的 R2 数据")

result_df_expanded.columns=['mse', 'rmse', 'mae', 'r2-2019', 'r2-2020', 'r2-2021',
       'r2-2022', 'r2-2023']

result_df_expanded

result_df_expanded.mean()

result_df_expanded.to_csv("空间自相关性+时间序列预测_最终版.csv")

#不带空间自相关性的代码-做对比实验

import pandas as pd
import numpy as np
import torch
import gpytorch
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import gc


def split_train_test_years(df, test_size=-5):
    """划分训练和测试年份"""
    years = df['year'].sort_values().unique()
    if abs(test_size) >= len(years):
        raise ValueError("Test size must be smaller than the total number of years")
    test_years = years[test_size:].tolist()
    train_years = years[:test_size].tolist()
    return train_years, test_years

def get_train_test_data(df, train_years, test_years):
    """根据年份划分训练和测试数据集"""
    train_df = df[df['year'].isin(train_years)]
    test_df = df[df['year'].isin(test_years)]
    return train_df, test_df

def standardize_data(df, feature_start_idx=2):
    """标准化特征数据"""
    features_scaled, scaler = standardize_features(df.iloc[:, feature_start_idx:])
    return pd.DataFrame(features_scaled, columns=df.iloc[:, feature_start_idx:].columns), scaler

def create_gp_model(X_train, y_train, device):
    """创建并返回高斯过程模型和似然函数"""
    class MultitaskGPModel(gpytorch.models.ExactGP):
        def __init__(self, train_x, train_y, likelihood):
            super(MultitaskGPModel, self).__init__(train_x, train_y, likelihood)
            self.mean_module = gpytorch.means.MultitaskMean(
                gpytorch.means.ConstantMean(), num_tasks=y_train.shape[1]
            )
            self.covar_module = gpytorch.kernels.MultitaskKernel(
                gpytorch.kernels.RBFKernel(), num_tasks=y_train.shape[1], rank=1
            )

        def forward(self, x):
            mean_x = self.mean_module(x)
            covar_x = self.covar_module(x)
            return gpytorch.distributions.MultitaskMultivariateNormal(mean_x, covar_x)

    likelihood = gpytorch.likelihoods.MultitaskGaussianLikelihood(num_tasks=y_train.shape[1]).to(device)
    model = MultitaskGPModel(X_train, y_train, likelihood).to(device)
    return model, likelihood

def train_model(model, likelihood, X_train, y_train, training_iter=80, lr=0.1):
    """训练高斯过程模型"""
    model.train()
    likelihood.train()
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)
    mll = gpytorch.mlls.ExactMarginalLogLikelihood(likelihood, model)

    for i in range(training_iter):
        optimizer.zero_grad()
        output = model(X_train)
        loss = -mll(output, y_train)
        loss.backward()
        print(f'Iter {i + 1}/{training_iter} - Loss: {loss.item():.3f}')
        optimizer.step()
    return model, likelihood

def predict_values(model, likelihood, train_len, pred_steps, device):
    """使用训练好的模型进行预测"""
    next_time_steps = torch.tensor([[train_len + 0] for i in range(pred_steps)],
                                  dtype=torch.float32).to(device) #
    model.eval()
    likelihood.eval()
    with torch.no_grad(), gpytorch.settings.fast_pred_var():
        next_predictions = likelihood(model(next_time_steps)).mean.cpu().numpy()
    return next_predictions

def cleanup_memory():
    """清理内存"""
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

def build_prediction_df(predictions, location, test_years, feature_cols):
    """构建预测结果 DataFrame"""
    pred_df = pd.DataFrame(predictions, columns=feature_cols)
    pred_df.insert(0, 'location', location)
    pred_df.insert(1, 'year', test_years)
    return pred_df

def evaluate_predictions(true_df, pred_df, feature_start_idx=2):
    """计算评估指标"""
    true_df = true_df.set_index('year')
    pred_df = pred_df.set_index('year')
    common_years = true_df.index.intersection(pred_df.index)
    feature_cols = true_df.columns[feature_start_idx:]

    mse_list, rmse_list, mae_list, r2_list = [], [], [], []
    for year in common_years:
        true_vals = true_df.loc[year, feature_cols].values
        pred_vals = pred_df.loc[year, feature_cols].values
        mse = mean_squared_error(true_vals, pred_vals)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(true_vals, pred_vals)
        r2 = r2_score(true_vals, pred_vals)
        mse_list.append(mse)
        rmse_list.append(rmse)
        mae_list.append(mae)
        r2_list.append(r2)
    
    return [mse_list, rmse_list, mae_list, r2_list]

def process_single_country(df, test_size, device):
    """处理单个国家的预测和评估"""
    # 划分年份和数据
    train_years, test_years = split_train_test_years(df, test_size)
    train_df, test_df = get_train_test_data(df, train_years, test_years)

    # 标准化训练数据
    features_scaled, scaler = standardize_data(train_df)
    
    # 准备训练输入
    X_train = torch.tensor(np.arange(len(features_scaled)).reshape(-1, 1), dtype=torch.float32).to(device)
    y_train = torch.tensor(features_scaled.values, dtype=torch.float32).to(device)

    # 创建和训练模型
    model, likelihood = create_gp_model(X_train, y_train, device)
    model, likelihood = train_model(model, likelihood, X_train, y_train)

    # 预测
    pred_steps = len(test_years)
    predictions = predict_values(model, likelihood, len(train_df), pred_steps, device)

    # 清理内存
    cleanup_memory()

    # 构建预测结果
    pred_df = build_prediction_df(predictions, train_df['location'].iloc[0], 
                                 test_years, features_scaled.columns)
    del X_train, y_train, model, likelihood
    gc.collect()
    torch.cuda.empty_cache()
    # 评估
    metrics = evaluate_predictions(test_df, pred_df)
    return metrics

def scale_result_dict(result_dict, outlier_threshold=1.5, handle_outliers='cap'):
    """
    将 result_dict 中所有国家的特征数据标准化到 [0, 1] 范围，并在标准化前检测异常值
    :param result_dict: 输入的原始数据字典
    :param outlier_threshold: IQR 倍数，用于定义异常值阈值（默认 1.5）
    :param handle_outliers: 处理异常值的方式，'cap'（限制到边界）、'remove'（移除）、'keep'（保留）
    :return: 标准化后的新字典 result_dict_scale
    """
    result_dict_scale = {}
    scaler = MinMaxScaler()

    for country, df in result_dict.items():
        # 提取特征列（从第2列开始）
        features = df.iloc[:, 2:].copy()
        
        # 检测和处理异常值
        if handle_outliers in ['cap', 'remove']:
            for col in features.columns:
                # 计算四分位距 (IQR)
                Q1 = features[col].quantile(0.25)
                Q3 = features[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - outlier_threshold * IQR
                upper_bound = Q3 + outlier_threshold * IQR

                # 检测异常值
                outliers = (features[col] < lower_bound) | (features[col] > upper_bound)
                if outliers.any():
                    print(f"Country {country}, Column {col}: Detected {outliers.sum()} outliers")
                
                # 处理异常值
                if handle_outliers == 'cap':
                    # 限制到边界值
                    features.loc[features[col] < lower_bound, col] = lower_bound
                    features.loc[features[col] > upper_bound, col] = upper_bound
                elif handle_outliers == 'remove':
                    # 移除包含异常值的行（注意：这会影响整个 DataFrame）
                    features = features[~outliers]
                    # 如果移除了行，同步更新 location 和 year
                    df = df.loc[features.index]

        # 标准化到 [0, 1]
        scaled_features = scaler.fit_transform(features)
        
        # 创建新的 DataFrame，保留 location 和 year 列
        scaled_df = pd.DataFrame(scaled_features, columns=features.columns)
        scaled_df.insert(0, 'location', df['location'].values)
        scaled_df.insert(1, 'year', df['year'].values)
        result_dict_scale[country] = scaled_df
    
    return result_dict_scale

#
#result_dict_scale = scale_result_dict(result_dict_test, outlier_threshold=10, handle_outliers='cap')
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
metric_dict = {}
test_size=-5
for j, country in enumerate(result_dict_scale.keys(), 1):
    df_target = result_dict_scale[country]
    metrics = process_single_country(df_target,test_size, device)
    metric_dict[country] = metrics

    # 打印结果
    avg_mse = np.mean(metrics[0][:5])
    avg_rmse = np.mean(metrics[1][:5])
    avg_mae = np.mean(metrics[2][:5])
    avg_r2 = np.mean(metrics[3][:5])
    print(f"Country: {country}, Avg MSE: {avg_mse:.3f}, Avg RMSE: {avg_rmse:.3f}, "
          f"Avg MAE: {avg_mae:.3f}, Avg R²: {avg_r2:.3f}")
    print(f"Processed {j}/{len(result_dict)} countries")


df=pd.DataFrame.from_dict(metric_dict).T.copy()


import pandas as pd
import numpy as np

# 任务 1：计算第 0、1、2 列的均值
mean_col_0 = df[0].apply(np.mean)
mean_col_1 = df[1].apply(np.mean)
mean_col_2 = df[2].apply(np.mean)
# 创建结果 DataFrame
result_df = pd.DataFrame({
    'Country': df.index,
    'Mean_Col_0': mean_col_0,
    'Mean_Col_1': mean_col_1,
    'Mean_Col_2': mean_col_2,
    '2015-2023 R2': df[3]  # 第 3 列保留不动，重命名为 "2015-2023 R2"
})

# 设置索引为国家名
result_df = result_df.set_index('Country')

print("任务 1：第 0、1、2 列的均值，第 3 列保留并重命名")
print(result_df)
# 定义年份列名
years = [str(year) for year in range(2019, 2024)]  # ['2015', '2016', ..., '2023']

# 将 "2015-2023 R2" 列的列表展开为单独的列
r2_expanded = pd.DataFrame(result_df['2015-2023 R2'].tolist(), columns=years, index=result_df.index)

# 删除原始的 "2015-2023 R2" 列，并与展开的年份列拼接
result_df_expanded = pd.concat([result_df.drop('2015-2023 R2', axis=1), r2_expanded], axis=1)

# 设置 Country 为索引（可选）
result_df_expanded = result_df_expanded

print("处理后的结果：每一列包含三个均值和 2015-2023 年的 R2 数据")


result_df_expanded

result_df_expanded.to_csv("9年预测结果_graph_emb+GP.csv")

import pandas as pd

result_df_expanded=pd.read_csv("9年预测结果_graph_emb+GP.csv",index_col=[0])

result_df_expanded

